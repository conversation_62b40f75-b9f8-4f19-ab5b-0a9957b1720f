#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单修复测试脚本
"""

import sys
import os
import time
from dotenv import load_dotenv

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 加载环境变量
load_dotenv()

def test_fix():
    """测试修复"""
    print("🔍 开始测试修复...")
    
    try:
        # 1. 测试环境变量
        print("📋 步骤1: 测试环境变量...")
        gate_key = os.getenv('GATE_API_KEY')
        bybit_key = os.getenv('BYBIT_API_KEY')
        okx_key = os.getenv('OKX_API_KEY')
        
        print(f"   GATE_API_KEY: {'✅ 已设置' if gate_key else '❌ 未设置'}")
        print(f"   BYBIT_API_KEY: {'✅ 已设置' if bybit_key else '❌ 未设置'}")
        print(f"   OKX_API_KEY: {'✅ 已设置' if okx_key else '❌ 未设置'}")
        
        if not (gate_key and bybit_key and okx_key):
            print("❌ 环境变量未正确设置")
            return False
            
        # 2. 测试交易所创建
        print("📋 步骤2: 测试交易所创建...")
        
        from exchanges.gate_exchange import GateExchange
        from exchanges.bybit_exchange import BybitExchange
        from exchanges.okx_exchange import OKXExchange
        
        # 创建交易所实例
        gate = GateExchange(gate_key, os.getenv('GATE_API_SECRET'))
        bybit = BybitExchange(bybit_key, os.getenv('BYBIT_API_SECRET'))
        okx = OKXExchange(okx_key, os.getenv('OKX_API_SECRET'), os.getenv('OKX_API_PASSPHRASE'))
        
        print("   ✅ 所有交易所实例创建成功")
        
        # 3. 测试交易规则预加载器
        print("📋 步骤3: 测试交易规则预加载器...")
        
        from core.trading_rules_preloader import TradingRulesPreloader
        
        preloader = TradingRulesPreloader()
        
        # 模拟设置全局交易所实例
        from core.trading_system_initializer import set_global_exchanges
        set_global_exchanges({
            'gate': gate,
            'bybit': bybit,
            'okx': okx
        })
        
        print("   ✅ 全局交易所实例已设置")
        
        # 4. 测试交易规则获取
        print("📋 步骤4: 测试交易规则获取...")
        
        test_cases = [
            ("gate", "SPK-USDT", "spot"),
            ("bybit", "SPK-USDT", "spot"),
            ("okx", "SPK-USDT", "spot")
        ]
        
        success_count = 0
        for exchange, symbol, market_type in test_cases:
            print(f"   测试: {exchange}_{symbol}_{market_type}")
            
            try:
                start_time = time.time()
                rule = preloader.get_trading_rule(exchange, symbol, market_type)
                end_time = time.time()
                
                if rule:
                    print(f"   ✅ 成功获取交易规则: {rule.qty_step} (耗时: {(end_time-start_time)*1000:.1f}ms)")
                    success_count += 1
                else:
                    print(f"   ❌ 无法获取交易规则")
            except Exception as e:
                print(f"   ❌ 获取交易规则异常: {e}")
                
        print(f"📋 测试结果: {success_count}/{len(test_cases)} 成功")
        
        if success_count > 0:
            print("🎉 修复成功！交易规则可以正常获取")
            return True
        else:
            print("❌ 修复失败，所有交易规则获取都失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_fix()
    if success:
        print("\n🎉 修复测试通过！")
    else:
        print("\n❌ 修复测试失败！")
