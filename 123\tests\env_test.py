#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
环境变量测试脚本
"""

import sys
import os
from dotenv import load_dotenv

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

print("🔍 测试环境变量加载...")

# 测试不同的.env文件路径
env_paths = [
    '.env',
    '../.env',
    os.path.join(os.path.dirname(os.path.dirname(__file__)), '.env'),
    os.path.join(os.getcwd(), '.env')
]

for env_path in env_paths:
    print(f"\n📋 测试路径: {env_path}")
    if os.path.exists(env_path):
        print(f"   ✅ 文件存在: {os.path.abspath(env_path)}")
        
        # 加载环境变量
        load_dotenv(env_path)
        
        # 测试API密钥
        gate_key = os.getenv('GATE_API_KEY')
        bybit_key = os.getenv('BYBIT_API_KEY')
        okx_key = os.getenv('OKX_API_KEY')
        
        print(f"   GATE_API_KEY: {'✅ 已设置' if gate_key else '❌ 未设置'}")
        print(f"   BYBIT_API_KEY: {'✅ 已设置' if bybit_key else '❌ 未设置'}")
        print(f"   OKX_API_KEY: {'✅ 已设置' if okx_key else '❌ 未设置'}")
        
        if gate_key and bybit_key and okx_key:
            print(f"   🎉 所有API密钥都已正确加载！")
            break
    else:
        print(f"   ❌ 文件不存在")

print(f"\n📋 当前工作目录: {os.getcwd()}")
print(f"📋 脚本所在目录: {os.path.dirname(__file__)}")
print(f"📋 项目根目录: {os.path.dirname(os.path.dirname(__file__))}")
