#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SPK-USDT_gate_spot交易规则获取失败问题精确诊断脚本
2025-07-30 深度分析
"""

import sys
import os
import asyncio
import logging
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(message)s')
logger = logging.getLogger(__name__)

async def diagnose_spk_usdt_gate_spot_issue():
    """精确诊断SPK-USDT_gate_spot交易规则获取失败问题"""
    
    logger.info("🔍 开始精确诊断SPK-USDT_gate_spot交易规则获取失败问题...")
    
    diagnosis_results = {
        "global_exchanges_status": None,
        "trading_rules_preloader_status": None,
        "gate_exchange_status": None,
        "api_keys_status": None,
        "system_initialization_status": None,
        "root_cause": None,
        "fix_recommendations": []
    }
    
    try:
        # 1. 检查全局交易所实例状态
        logger.info("📋 步骤1: 检查全局交易所实例状态...")
        try:
            from core.trading_system_initializer import get_global_exchanges
            global_exchanges = get_global_exchanges()
            
            if global_exchanges is None:
                diagnosis_results["global_exchanges_status"] = "❌ None - 全局交易所实例未设置"
                diagnosis_results["root_cause"] = "全局交易所实例未设置，导致交易规则预加载器无法获取交易所实例"
                diagnosis_results["fix_recommendations"].append("在系统初始化时调用set_global_exchanges()设置全局交易所实例")
            else:
                diagnosis_results["global_exchanges_status"] = f"✅ 已设置 - 包含{len(global_exchanges)}个交易所: {list(global_exchanges.keys())}"
                
        except Exception as e:
            diagnosis_results["global_exchanges_status"] = f"❌ 导入错误: {e}"
            
        # 2. 检查交易规则预加载器状态
        logger.info("📋 步骤2: 检查交易规则预加载器状态...")
        try:
            from core.trading_rules_preloader import TradingRulesPreloader
            
            # 尝试创建预加载器实例
            preloader = TradingRulesPreloader()
            diagnosis_results["trading_rules_preloader_status"] = "✅ 可以正常创建实例"
            
            # 检查是否能获取SPK-USDT_gate_spot规则
            rule = preloader.get_trading_rule("gate", "SPK-USDT", "spot")
            if rule:
                diagnosis_results["trading_rules_preloader_status"] += f" - SPK-USDT_gate_spot规则存在: {rule.qty_step}"
            else:
                diagnosis_results["trading_rules_preloader_status"] += " - SPK-USDT_gate_spot规则不存在"
                
        except Exception as e:
            diagnosis_results["trading_rules_preloader_status"] = f"❌ 创建失败: {e}"
            
        # 3. 检查Gate.io交易所状态
        logger.info("📋 步骤3: 检查Gate.io交易所状态...")
        try:
            from exchanges.gate_exchange import GateExchange
            
            # 检查是否能创建Gate.io实例
            diagnosis_results["gate_exchange_status"] = "✅ 可以导入GateExchange类"
            
        except Exception as e:
            diagnosis_results["gate_exchange_status"] = f"❌ 导入失败: {e}"
            
        # 4. 检查API密钥配置状态
        logger.info("📋 步骤4: 检查API密钥配置状态...")
        try:
            api_keys_configured = 0
            total_keys_needed = 7  # Gate.io需要2个，Bybit需要2个，OKX需要3个
            
            # Gate.io API密钥 (正确的环境变量名)
            gate_keys = [
                os.getenv("GATE_API_KEY"),
                os.getenv("GATE_API_SECRET")  # 修复：正确的变量名
            ]
            gate_configured = sum(1 for key in gate_keys if key)
            api_keys_configured += gate_configured
            
            # Bybit API密钥 (正确的环境变量名)
            bybit_keys = [
                os.getenv("BYBIT_API_KEY"),
                os.getenv("BYBIT_API_SECRET")  # 修复：正确的变量名
            ]
            bybit_configured = sum(1 for key in bybit_keys if key)
            api_keys_configured += bybit_configured
            
            # OKX API密钥 (正确的环境变量名)
            okx_keys = [
                os.getenv("OKX_API_KEY"),
                os.getenv("OKX_API_SECRET"),  # 修复：正确的变量名
                os.getenv("OKX_API_PASSPHRASE")
            ]
            okx_configured = sum(1 for key in okx_keys if key)
            api_keys_configured += okx_configured
            
            diagnosis_results["api_keys_status"] = f"配置了{api_keys_configured}/{total_keys_needed}个API密钥 (Gate:{gate_configured}/2, Bybit:{bybit_configured}/2, OKX:{okx_configured}/3)"
            
            if api_keys_configured == 0:
                diagnosis_results["fix_recommendations"].append("配置API密钥到.env文件中")
                
        except Exception as e:
            diagnosis_results["api_keys_status"] = f"❌ 检查失败: {e}"
            
        # 5. 检查系统初始化状态
        logger.info("📋 步骤5: 检查系统初始化状态...")
        try:
            from core.trading_system_initializer import TradingSystemInitializer
            
            # 检查initialize_all_systems方法是否存在
            initializer = TradingSystemInitializer()
            if hasattr(initializer, 'initialize_all_systems'):
                diagnosis_results["system_initialization_status"] = "✅ initialize_all_systems方法存在"
                
                # 检查方法中是否调用了set_global_exchanges
                import inspect
                source = inspect.getsource(initializer.initialize_all_systems)
                if 'set_global_exchanges' in source:
                    diagnosis_results["system_initialization_status"] += " - 包含set_global_exchanges调用"
                else:
                    diagnosis_results["system_initialization_status"] += " - ❌ 缺少set_global_exchanges调用"
                    diagnosis_results["fix_recommendations"].append("在initialize_all_systems方法中添加set_global_exchanges调用")
            else:
                diagnosis_results["system_initialization_status"] = "❌ initialize_all_systems方法不存在"
                
        except Exception as e:
            diagnosis_results["system_initialization_status"] = f"❌ 检查失败: {e}"
            
        # 6. 根本原因分析
        logger.info("📋 步骤6: 根本原因分析...")
        
        if diagnosis_results["global_exchanges_status"] and "None" in diagnosis_results["global_exchanges_status"]:
            diagnosis_results["root_cause"] = "核心问题：get_global_exchanges()返回None，导致交易规则预加载器无法获取交易所实例进行API调用"
            diagnosis_results["fix_recommendations"].extend([
                "在系统启动时确保调用set_global_exchanges()设置全局交易所实例",
                "检查initialize_all_systems()方法是否正确执行",
                "确保交易所实例初始化成功后再设置全局实例"
            ])
        elif "配置了0/" in diagnosis_results.get("api_keys_status", ""):
            diagnosis_results["root_cause"] = "次要问题：API密钥未配置，但系统应该有兜底机制处理这种情况"
            diagnosis_results["fix_recommendations"].append("配置API密钥以启用完整功能")
        else:
            diagnosis_results["root_cause"] = "需要进一步分析，可能是系统初始化流程问题"
            
    except Exception as e:
        logger.error(f"❌ 诊断过程中发生异常: {e}")
        diagnosis_results["root_cause"] = f"诊断异常: {e}"
        
    # 输出诊断结果
    logger.info("🎯 诊断结果汇总:")
    logger.info("=" * 80)
    
    for key, value in diagnosis_results.items():
        if key == "fix_recommendations":
            if value:
                logger.info(f"🔧 修复建议:")
                for i, rec in enumerate(value, 1):
                    logger.info(f"   {i}. {rec}")
        else:
            logger.info(f"📊 {key}: {value}")
            
    logger.info("=" * 80)
    
    # 返回诊断结果
    return diagnosis_results

if __name__ == "__main__":
    asyncio.run(diagnose_spk_usdt_gate_spot_issue())