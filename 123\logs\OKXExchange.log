2025-07-30 14:19:42 [DEBUG] [OKXExchange] ✅ OKX统一模块初始化完成
2025-07-30 14:19:42 [DEBUG] [OKXExchange] ✅ OKX配置加载完成: 杠杆=3x
2025-07-30 14:19:42 [INFO] [OKXExchange] ✅ OKX交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-30 14:19:42 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 14:19:42.198 [INFO] [exchanges.okx_exchange] 🔧 OKX API限制根源修复为2次/秒，确保30+代币健壮启动
2025-07-30 14:19:42.198 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=2次/秒
2025-07-30 14:19:42 [INFO] [OKXExchange] 🔥 开始初始化OKX账户配置...
2025-07-30 14:19:42 [INFO] [OKXExchange] OKX当前账户模式: 2
2025-07-30 14:19:42 [WARNING] [OKXExchange] OKX账户不是跨币种保证金模式，当前模式: 2
2025-07-30 14:19:47 [INFO] [OKXExchange] OKX设置为单向持仓模式
2025-07-30 14:19:47.323 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SPK-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 14:19:52.343 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 14:19:52.343 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 14:19:57.333 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 14:19:57.333 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 14:19:57 [DEBUG] [OKXExchange] OKX预设置杠杆成功: SPK-USDT
2025-07-30 14:19:57.333 [INFO] [exchanges.okx_exchange] OKX设置杠杆: RESOLV-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 14:20:02.350 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 14:20:02.350 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 14:20:07.329 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 14:20:07.330 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 14:20:07 [DEBUG] [OKXExchange] OKX预设置杠杆成功: RESOLV-USDT
2025-07-30 14:20:07.330 [INFO] [exchanges.okx_exchange] OKX设置杠杆: ICNT-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 14:20:12.340 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 14:20:12.340 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 14:20:17.330 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 14:20:17.330 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 14:20:17 [DEBUG] [OKXExchange] OKX预设置杠杆成功: ICNT-USDT
2025-07-30 14:20:17.330 [INFO] [exchanges.okx_exchange] OKX设置杠杆: CAKE-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 14:20:22.342 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 14:20:22.342 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 14:20:27.327 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 14:20:27.328 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 14:20:27 [DEBUG] [OKXExchange] OKX预设置杠杆成功: CAKE-USDT
2025-07-30 14:20:27 [INFO] [OKXExchange] ✅ OKX账户初始化完成
2025-07-30 14:20:32.402 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-07-30 14:20:37.324 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-07-30 14:20:42.330 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-07-30 14:20:47.348 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-07-30 14:20:52.342 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-07-30 15:16:42 [DEBUG] [OKXExchange] ✅ OKX统一模块初始化完成
2025-07-30 15:16:42 [DEBUG] [OKXExchange] ✅ OKX配置加载完成: 杠杆=3x
2025-07-30 15:16:42 [INFO] [OKXExchange] ✅ OKX交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-30 15:16:42 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 15:16:42.182 [INFO] [exchanges.okx_exchange] 🔧 OKX API限制根源修复为2次/秒，确保30+代币健壮启动
2025-07-30 15:16:42.182 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=2次/秒
2025-07-30 15:16:42 [INFO] [OKXExchange] 🔥 开始初始化OKX账户配置...
2025-07-30 15:16:42 [INFO] [OKXExchange] OKX当前账户模式: 2
2025-07-30 15:16:42 [WARNING] [OKXExchange] OKX账户不是跨币种保证金模式，当前模式: 2
2025-07-30 15:16:47 [INFO] [OKXExchange] OKX设置为单向持仓模式
2025-07-30 15:16:47.326 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SPK-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 15:16:52.298 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 15:16:52.299 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 15:16:57.301 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 15:16:57.301 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 15:16:57 [DEBUG] [OKXExchange] OKX预设置杠杆成功: SPK-USDT
2025-07-30 15:16:57.302 [INFO] [exchanges.okx_exchange] OKX设置杠杆: RESOLV-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 15:17:02.303 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 15:17:02.303 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 15:17:07.319 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 15:17:07.319 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 15:17:07 [DEBUG] [OKXExchange] OKX预设置杠杆成功: RESOLV-USDT
2025-07-30 15:17:07.319 [INFO] [exchanges.okx_exchange] OKX设置杠杆: ICNT-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 15:17:12.322 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 15:17:12.322 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 15:17:17.317 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 15:17:17.318 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 15:17:17 [DEBUG] [OKXExchange] OKX预设置杠杆成功: ICNT-USDT
2025-07-30 15:17:17.318 [INFO] [exchanges.okx_exchange] OKX设置杠杆: CAKE-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 15:17:22.302 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 15:17:22.302 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 15:17:27.353 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 15:17:27.353 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 15:17:27 [DEBUG] [OKXExchange] OKX预设置杠杆成功: CAKE-USDT
2025-07-30 15:17:27 [INFO] [OKXExchange] ✅ OKX账户初始化完成
2025-07-30 15:17:32.311 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-07-30 15:17:37.298 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
