#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SPK-USDT_gate_spot交易规则获取失败问题修复验证脚本
2025-07-30 修复验证
"""

import sys
import os
import asyncio
import logging
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(message)s')
logger = logging.getLogger(__name__)

async def verify_fix():
    """验证修复效果"""
    
    logger.info("🔍 开始验证SPK-USDT_gate_spot交易规则获取失败问题修复效果...")
    
    verification_results = {
        "global_exchanges_before": None,
        "global_exchanges_after": None,
        "trading_rule_test": None,
        "system_initialization_test": None,
        "temporary_instance_test": None,
        "fix_success": False
    }
    
    try:
        # 1. 检查修复前的全局交易所实例状态
        logger.info("📋 步骤1: 检查修复前全局交易所实例状态...")
        try:
            from core.trading_system_initializer import get_global_exchanges
            global_exchanges_before = get_global_exchanges()
            verification_results["global_exchanges_before"] = f"修复前: {global_exchanges_before}"
            
        except Exception as e:
            verification_results["global_exchanges_before"] = f"❌ 检查失败: {e}"
            
        # 2. 执行系统初始化测试
        logger.info("📋 步骤2: 执行系统初始化测试...")
        try:
            from core.trading_system_initializer import TradingSystemInitializer
            
            initializer = TradingSystemInitializer()
            
            # 模拟系统初始化
            success = await initializer.initialize_all_systems()
            verification_results["system_initialization_test"] = f"初始化结果: {success}"
            
        except Exception as e:
            verification_results["system_initialization_test"] = f"❌ 初始化失败: {e}"
            
        # 3. 检查修复后的全局交易所实例状态
        logger.info("📋 步骤3: 检查修复后全局交易所实例状态...")
        try:
            global_exchanges_after = get_global_exchanges()
            if global_exchanges_after is not None:
                verification_results["global_exchanges_after"] = f"修复后: {type(global_exchanges_after)} - {list(global_exchanges_after.keys()) if isinstance(global_exchanges_after, dict) else 'Not a dict'}"
            else:
                verification_results["global_exchanges_after"] = "修复后: None"
                
        except Exception as e:
            verification_results["global_exchanges_after"] = f"❌ 检查失败: {e}"
            
        # 4. 测试交易规则获取
        logger.info("📋 步骤4: 测试SPK-USDT_gate_spot交易规则获取...")
        try:
            from core.trading_rules_preloader import TradingRulesPreloader
            
            preloader = TradingRulesPreloader()
            rule = preloader.get_trading_rule("gate", "SPK-USDT", "spot")
            
            if rule:
                verification_results["trading_rule_test"] = f"✅ 成功获取规则: qty_step={rule.qty_step}, exchange={rule.exchange}, market_type={rule.market_type}"
            else:
                verification_results["trading_rule_test"] = "❌ 无法获取交易规则"
                
        except Exception as e:
            verification_results["trading_rule_test"] = f"❌ 测试失败: {e}"
            
        # 5. 测试临时实例创建机制
        logger.info("📋 步骤5: 测试临时实例创建机制...")
        try:
            from core.trading_rules_preloader import TradingRulesPreloader
            
            preloader = TradingRulesPreloader()
            
            # 测试三个交易所的临时实例创建
            temp_results = {}
            for exchange in ["gate", "bybit", "okx"]:
                try:
                    temp_instance = preloader._create_temporary_exchange_instance_sync(exchange)
                    if temp_instance:
                        temp_results[exchange] = "✅ 创建成功"
                    else:
                        temp_results[exchange] = "❌ 创建失败"
                except Exception as temp_e:
                    temp_results[exchange] = f"❌ 异常: {temp_e}"
                    
            verification_results["temporary_instance_test"] = temp_results
            
        except Exception as e:
            verification_results["temporary_instance_test"] = f"❌ 测试失败: {e}"
            
        # 6. 综合评估修复效果
        logger.info("📋 步骤6: 综合评估修复效果...")
        
        # 检查关键指标
        global_exchanges_fixed = "修复后:" in str(verification_results.get("global_exchanges_after", "")) and "None" not in str(verification_results.get("global_exchanges_after", ""))
        trading_rule_working = "✅ 成功获取规则" in str(verification_results.get("trading_rule_test", ""))
        temp_instance_working = isinstance(verification_results.get("temporary_instance_test"), dict) and any("✅" in str(v) for v in verification_results["temporary_instance_test"].values())
        
        if global_exchanges_fixed and trading_rule_working:
            verification_results["fix_success"] = True
            logger.info("🎉 修复成功！全局交易所实例已正确设置，交易规则获取正常")
        elif trading_rule_working and temp_instance_working:
            verification_results["fix_success"] = True
            logger.info("🎉 修复成功！虽然全局实例可能有问题，但临时实例创建机制工作正常")
        else:
            verification_results["fix_success"] = False
            logger.warning("⚠️ 修复可能不完整，需要进一步检查")
            
    except Exception as e:
        logger.error(f"❌ 验证过程中发生异常: {e}")
        verification_results["fix_success"] = False
        
    # 输出验证结果
    logger.info("🎯 修复验证结果汇总:")
    logger.info("=" * 80)
    
    for key, value in verification_results.items():
        if key == "temporary_instance_test" and isinstance(value, dict):
            logger.info(f"📊 {key}:")
            for exchange, result in value.items():
                logger.info(f"   {exchange}: {result}")
        else:
            logger.info(f"📊 {key}: {value}")
            
    logger.info("=" * 80)
    
    if verification_results["fix_success"]:
        logger.info("🎉 修复验证通过！SPK-USDT_gate_spot交易规则获取问题已解决")
    else:
        logger.warning("⚠️ 修复验证未完全通过，可能需要进一步调整")
    
    # 返回验证结果
    return verification_results

if __name__ == "__main__":
    asyncio.run(verify_fix())