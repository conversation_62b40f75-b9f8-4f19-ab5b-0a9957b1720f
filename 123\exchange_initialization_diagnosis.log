2025-07-30 22:05:10,599 [INFO] 🔍 开始交易所初始化诊断...
2025-07-30 22:05:10,599 [INFO] 🔍 1. 检查环境变量配置...
2025-07-30 22:05:10,600 [INFO]    环境变量配置: 0/7
2025-07-30 22:05:10,600 [INFO]    Gate.io配置: ❌
2025-07-30 22:05:10,600 [INFO]    Bybit配置: ❌
2025-07-30 22:05:10,600 [INFO]    OKX配置: ❌
2025-07-30 22:05:10,601 [INFO] 🔍 2. 测试单独的交易所初始化...
2025-07-30 22:05:10,601 [WARNING]    ❌ Gate.io API密钥缺失
2025-07-30 22:05:10,601 [WARNING]    ❌ Bybit API密钥缺失
2025-07-30 22:05:10,601 [WARNING]    ❌ OKX API密钥缺失
2025-07-30 22:05:10,601 [INFO] 🔍 3. 测试initialize_exchanges方法...
2025-07-30 22:05:11,164 [INFO] TelegramNotifier初始化:
2025-07-30 22:05:11,165 [INFO]   Bot Token: 已设置
2025-07-30 22:05:11,165 [INFO]   Chat ID: 已设置
2025-07-30 22:05:11,165 [INFO]   Base URL: https://api.telegram.org/bot7954850653:AAHOIyX61SF1oDDxZBVclzfdmJhX-Jbn0-0/sendMessage
2025-07-30 22:05:11,165 [INFO] ✅ Telegram配置完整，通知功能已启用
2025-07-30 22:05:11,166 [INFO] 已启用的通知器: ['telegram', 'console']
2025-07-30 22:05:11,186 [INFO] ✅ 交易系统初始化器创建完成
2025-07-30 22:05:11,187 [INFO]    调用initialize_exchanges()...
2025-07-30 22:05:11,187 [INFO] 🏪 独立初始化交易所...
2025-07-30 22:05:11,187 [INFO] 初始化gate交易所接口，API请求限制: 10/秒
2025-07-30 22:05:11,187 [INFO] 🔧 Gate API限制根源修复为8次/秒，确保30+代币健壮启动
2025-07-30 22:05:11,188 [INFO] 初始化Gate.io交易所接口，API请求限制: 8/秒
2025-07-30 22:05:11,189 [INFO] 🚀 通用代币系统初始化: 支持10个代币
2025-07-30 22:05:11,190 [INFO] ✅ 加载交易对缓存: 10个交易对
2025-07-30 22:05:11,190 [INFO] 📅 缓存时间: Wed Jul 30 21:28:42 2025
2025-07-30 22:05:11,190 [INFO] 📋 使用缓存的交易对: 10个
2025-07-30 22:05:11,191 [INFO] 🚀 从通用代币系统加载预加载交易对: 10个
2025-07-30 22:05:11,191 [INFO] ✅ 交易规则预加载器初始化完成
2025-07-30 22:05:11,192 [INFO]    缓存过期时间: 24小时
2025-07-30 22:05:11,192 [INFO]    预加载交易对数量: 10
2025-07-30 22:05:11,192 [INFO] ✅ 统一开仓管理器初始化完成
2025-07-30 22:05:11,193 [INFO]    🔥 使用API动态精度，删除硬编码
2025-07-30 22:05:11,193 [INFO]    🔥 步长缓存机制，最高速度
2025-07-30 22:05:11,193 [INFO]    🔥 严格截断，不四舍五入
2025-07-30 22:05:11,193 [INFO] ✅ 统一平仓管理器初始化完成
2025-07-30 22:05:11,194 [INFO]    🔥 API精度+步长+缓存+重试机制
2025-07-30 22:05:11,194 [INFO]    🔥 严格截取，绝不四舍五入
2025-07-30 22:05:11,194 [INFO]    最大重试次数: 3
2025-07-30 22:05:11,195 [INFO]    重试精度序列: [6, 4, 2, 1]
2025-07-30 22:05:11,195 [INFO] ✅ Gate.io交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-30 22:05:11,196 [INFO] ✅ 统一HTTP会话管理器初始化完成
2025-07-30 22:05:11,196 [INFO] 🚀 初始化Gate.io交易所（分离账户模式）...
2025-07-30 22:05:11,217 [WARNING] ⚠️ SSL验证已禁用 - 仅适用于开发/测试环境
2025-07-30 22:05:11,217 [INFO] ✅ 统一网络配置管理器初始化完成
2025-07-30 22:05:11,217 [INFO]    🔥 连接超时: 5.0秒
2025-07-30 22:05:11,218 [INFO]    🔥 总超时: 10.0秒
2025-07-30 22:05:11,218 [INFO]    🔥 最大重试: 3次
2025-07-30 22:05:11,218 [INFO]    🔥 重试延迟: 50ms
2025-07-30 22:05:11,235 [INFO] ✅ 创建新HTTP会话: gate (总计: 1)
2025-07-30 22:05:13,269 [INFO] ✅ Gate.io连接成功，服务器时间: ****************
2025-07-30 22:05:13,269 [INFO] ✅ Gate.io使用分离账户模式
2025-07-30 22:05:13,272 [DEBUG] Gate.io请求: GET https://api.gateio.ws/api/v4/spot/accounts, Headers: {'KEY': 'eb321587106cf01dcde93830d7777e2a', 'SIGN': 'fc81142624b96fb68d3f7b582d0940fa5e28719e5fd14f58da3f16a074ca72451c7ca099c1ba1d55358dc6e624d943a449a7a048f21579c27784a2b2fd28e5c5', 'Timestamp': '**********.2720728', 'Content-Type': 'application/json'}, Data: 
2025-07-30 22:05:13,658 [DEBUG] Gate.io API响应成功 (状态码200): [{'currency': 'ICNT', 'available': '0.00695', 'locked': '0', 'update_id': 40}, {'currency': 'HUMA', 'available': '0.00087', 'locked': '0', 'update_id': 14}, {'currency': 'TMAI', 'available': '0.********', 'locked': '0', 'update_id': 53}, {'currency': 'DOGE', 'available': '0.00099', 'locked': '0', 'update_id': 4}, {'currency': 'USDT', 'available': '103.************', 'locked': '0', 'update_id': 1483}, {'currency': 'NEAR', 'available': '0.00799', 'locked': '0', 'update_id': 218}, {'currency': 'GT', 'available': '0.**********', 'locked': '0', 'update_id': 634}, {'currency': 'ALCH', 'available': '0.08', 'locked': '0', 'update_id': 26}, {'currency': 'SPK', 'available': '0.00943', 'locked': '0', 'update_id': 9}, {'currency': 'PEPE', 'available': '0.039', 'locked': '0', 'update_id': 6}, {'currency': '4EVER', 'available': '0.02703235', 'locked': '0', 'update_id': 26}, {'currency': 'GLS', 'available': '0.01086977', 'locked': '0', 'update_id': 219}, {'currency': 'BTC', 'available': '0.00000786', 'locked': '0', 'update_id': 93}, {'currency': 'UNI', 'available': '0.00952', 'locked': '0', 'update_id': 62}, {'currency': 'BNB', 'available': '0.000035', 'locked': '0', 'update_id': 20}, {'currency': 'ETH', 'available': '0.0000901', 'locked': '0', 'update_id': 6}, {'currency': 'RESOLV', 'available': '0.00609', 'locked': '0', 'update_id': 109}, {'currency': 'LINK', 'available': '0.00141', 'locked': '0', 'update_id': 12}, {'currency': 'PI', 'available': '0.00883', 'locked': '0', 'update_id': 4}, {'currency': 'LTC', 'available': '0.0000506', 'locked': '0', 'update_id': 25}, {'currency': 'POINT', 'available': '0.**********', 'locked': '0', 'update_id': 17}, {'currency': 'LAYER', 'available': '0', 'locked': '0', 'update_id': 0}, {'currency': 'ADA', 'available': '0.00216', 'locked': '0', 'update_id': 117}]
2025-07-30 22:05:13,660 [INFO] 🕐 gate 健壮冷却等待: 1.112秒 (要求: 1.500秒)
2025-07-30 22:05:14,778 [DEBUG] Gate.io请求: GET https://api.gateio.ws/api/v4/futures/usdt/accounts, Headers: {'KEY': 'eb321587106cf01dcde93830d7777e2a', 'SIGN': '10d089f4a068c839a84e766c62be1c7ccc85a3ebd5b9f6b7644cbcc2d43fd89b1a0c687f9b816dae51c88c3b49b8d11606620ba431838e3b89ffeae8bb12055e', 'Timestamp': '**********.7787511', 'Content-Type': 'application/json'}, Data: 
2025-07-30 22:05:15,140 [DEBUG] Gate.io API响应成功 (状态码200): {'order_margin': '0', 'point': '0', 'bonus': '0', 'history': {'dnw': '-124.********', 'pnl': '262.***********', 'refr': '0', 'point_fee': '0', 'fund': '1.***********', 'bonus_dnw': '0', 'point_refr': '0', 'bonus_offset': '0', 'fee': '-34.********', 'point_dnw': '0', 'cross_settle': '0'}, 'unrealised_pnl': '0', 'total': '104.***********', 'available': '104.***********', 'enable_credit': False, 'in_dual_mode': False, 'currency': 'USDT', 'position_margin': '0', 'user': ********, 'update_time': **********, 'update_id': 1158, 'position_initial_margin': '0', 'maintenance_margin': '0', 'margin_mode': 0, 'enable_evolved_classic': True, 'cross_initial_margin': '0', 'cross_maintenance_margin': '0', 'cross_order_margin': '0', 'cross_unrealised_pnl': '0', 'cross_virtual_unrealised_pnl': '0', 'cross_available': '104.***********', 'isolated_position_margin': '0', 'enable_new_dual_mode': False, 'margin_mode_name': 'classic', 'cross_margin_balance': '104.***********', 'cross_mmr': '0', 'cross_imr': '0', 'enable_tiered_mm': False, 'position_voucher_total': '0'}
2025-07-30 22:05:15,142 [INFO] ✅ Gate.io初始余额: 现货103.44 + 期货104.64 = 总计208.08 USDT
2025-07-30 22:05:15,142 [INFO] ✅ Gate.io分离账户模式已确认
2025-07-30 22:05:15,143 [INFO] ✅ Gate.io交易所初始化完成
2025-07-30 22:05:15,143 [INFO] ✅ Gate.io初始化成功
2025-07-30 22:05:15,144 [INFO] 初始化bybit交易所接口，API请求限制: 6/秒
2025-07-30 22:05:15,144 [INFO] 🔧 Bybit API限制根源修复为4次/秒，确保30+代币健壮启动
2025-07-30 22:05:15,145 [INFO] 初始化Bybit交易所接口，API请求限制: 4/秒
2025-07-30 22:05:15,146 [INFO] ✅ Bybit交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-30 22:05:15,151 [INFO] 🔥 网络配置环境变量已应用
2025-07-30 22:05:15,151 [INFO] 📋 使用缓存的交易对: 10个
2025-07-30 22:05:15,152 [INFO] ✅ 动态阈值系统已启用，无需传统阈值验证
2025-07-30 22:05:15,153 [INFO] ✅ Bybit交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-30 22:05:15,153 [DEBUG] Bybit交易所初始化完成: 杠杆=3x, 最小价值=35.0USDT
2025-07-30 22:05:15,154 [INFO] 🚀 初始化Bybit交易所（统一账户模式）...
2025-07-30 22:05:15,176 [WARNING] ⚠️ SSL验证已禁用 - 仅适用于开发/测试环境
2025-07-30 22:05:15,185 [INFO] ✅ 创建新HTTP会话: bybit (总计: 2)
2025-07-30 22:05:15,186 [DEBUG] Bybit请求: GET https://api.bybit.com/v5/market/time
2025-07-30 22:05:15,186 [DEBUG] Bybit请求头: {'Content-Type': 'application/json'}
2025-07-30 22:05:15,187 [DEBUG] Bybit请求参数: {}
2025-07-30 22:05:16,597 [ERROR] Bybit请求失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/time'
2025-07-30 22:05:16,598 [WARNING] 获取Bybit服务器时间失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/time'，使用本地时间
2025-07-30 22:05:16,599 [INFO] ✅ Bybit连接成功，服务器时间: *************
2025-07-30 22:05:16,600 [INFO] ✅ Bybit使用统一账户模式
2025-07-30 22:05:16,600 [INFO] Bybit查询余额: 原始account_type=unified, 强制使用category=UNIFIED
2025-07-30 22:05:16,600 [INFO] 🕐 bybit 健壮冷却等待: 0.085秒 (要求: 1.500秒)
2025-07-30 22:05:16,935 [ERROR] Bybit同步时间异常: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/time'
2025-07-30 22:05:16,936 [WARNING] Bybit时间同步失败，使用本地时间
2025-07-30 22:05:16,937 [DEBUG] Bybit固定时间戳: *************
2025-07-30 22:05:16,937 [DEBUG] Bybit签名构建:
2025-07-30 22:05:16,937 [DEBUG]   timestamp: *************
2025-07-30 22:05:16,938 [DEBUG]   api_key: lYC8LeR8***
2025-07-30 22:05:16,939 [DEBUG]   recv_window: 5000
2025-07-30 22:05:16,939 [DEBUG]   param_str: accountType=UNIFIED
2025-07-30 22:05:16,940 [DEBUG]   完整签名字符串长度: 54
2025-07-30 22:05:16,940 [DEBUG]   生成的签名: 79bb47d6c5751cf4...
2025-07-30 22:05:16,941 [DEBUG] Bybit请求: GET https://api.bybit.com/v5/account/wallet-balance?accountType=UNIFIED
2025-07-30 22:05:16,941 [DEBUG] Bybit请求头: {'Content-Type': 'application/json', 'X-BAPI-API-KEY': 'lYC8LeR8sWnAiDZFrZ', 'X-BAPI-TIMESTAMP': '*************', 'X-BAPI-RECV-WINDOW': '5000', 'X-BAPI-SIGN-TYPE': '2', 'X-BAPI-SIGN': '79bb47d6c5751cf476b2eed918119c474e6a926e8207ede76c611d57b75be1d6'}
2025-07-30 22:05:16,942 [DEBUG] Bybit请求参数: {'accountType': 'UNIFIED'}
2025-07-30 22:05:17,187 [ERROR] Bybit请求失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/account/wallet-balance?accountType=UNIFIED'
2025-07-30 22:05:17,189 [ERROR] 获取Bybit余额失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/account/wallet-balance?accountType=UNIFIED'
2025-07-30 22:05:17,189 [INFO] ✅ Bybit初始余额: 0.00 USDT
2025-07-30 22:05:17,190 [INFO] ✅ Bybit统一账户模式已激活
2025-07-30 22:05:17,190 [INFO] ✅ Bybit交易所初始化完成
2025-07-30 22:05:17,191 [INFO] ✅ Bybit初始化成功
2025-07-30 22:05:17,192 [INFO] 初始化OKX交易所接口，API请求限制: 5/秒
2025-07-30 22:05:17,195 [INFO] 🔧 OKX API限制根源修复为2次/秒，确保30+代币健壮启动
2025-07-30 22:05:17,195 [INFO] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=2次/秒
2025-07-30 22:05:17,215 [WARNING] ⚠️ SSL验证已禁用 - 仅适用于开发/测试环境
2025-07-30 22:05:17,224 [INFO] ✅ 创建新HTTP会话: okx (总计: 3)
2025-07-30 22:05:19,434 [INFO] 🕐 okx 健壮冷却等待: 2.790秒 (要求: 5.000秒)
2025-07-30 22:05:22,620 [INFO] 📋 使用缓存的交易对: 10个
2025-07-30 22:05:22,621 [INFO] OKX设置杠杆: SPK-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 22:05:22,621 [INFO] 🕐 okx 健壮冷却等待: 4.596秒 (要求: 5.000秒)
2025-07-30 22:05:27,930 [INFO] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 22:05:27,931 [INFO] OKX持仓模式: net_mode
2025-07-30 22:05:27,931 [INFO] 🕐 okx 健壮冷却等待: 4.295秒 (要求: 5.000秒)
2025-07-30 22:05:33,032 [INFO] OKX设置net杠杆成功: 3倍
2025-07-30 22:05:33,032 [INFO] OKX设置杠杆成功: 3倍
2025-07-30 22:05:33,033 [INFO] OKX设置杠杆: RESOLV-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 22:05:33,034 [INFO] 🕐 okx 健壮冷却等待: 4.198秒 (要求: 5.000秒)
2025-07-30 22:05:37,658 [INFO] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 22:05:37,658 [INFO] OKX持仓模式: net_mode
2025-07-30 22:05:37,659 [INFO] 🕐 okx 健壮冷却等待: 4.578秒 (要求: 5.000秒)
2025-07-30 22:05:42,668 [INFO] OKX设置net杠杆成功: 3倍
2025-07-30 22:05:42,668 [INFO] OKX设置杠杆成功: 3倍
2025-07-30 22:05:42,669 [INFO] OKX设置杠杆: ICNT-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 22:05:42,669 [INFO] 🕐 okx 健壮冷却等待: 4.576秒 (要求: 5.000秒)
2025-07-30 22:05:47,665 [INFO] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 22:05:47,666 [INFO] OKX持仓模式: net_mode
2025-07-30 22:05:47,667 [INFO] 🕐 okx 健壮冷却等待: 4.597秒 (要求: 5.000秒)
2025-07-30 22:05:52,669 [INFO] OKX设置net杠杆成功: 3倍
2025-07-30 22:05:52,669 [INFO] OKX设置杠杆成功: 3倍
2025-07-30 22:05:52,670 [INFO] OKX设置杠杆: CAKE-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 22:05:52,671 [INFO] 🕐 okx 健壮冷却等待: 4.595秒 (要求: 5.000秒)
2025-07-30 22:05:57,688 [INFO] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 22:05:57,689 [INFO] OKX持仓模式: net_mode
2025-07-30 22:05:57,689 [INFO] 🕐 okx 健壮冷却等待: 4.584秒 (要求: 5.000秒)
2025-07-30 22:06:03,044 [INFO] OKX设置net杠杆成功: 3倍
2025-07-30 22:06:03,044 [INFO] OKX设置杠杆成功: 3倍
2025-07-30 22:06:03,045 [INFO] ✅ OKX初始化成功
2025-07-30 22:06:03,046 [INFO] ✅ 成功初始化 3 个交易所: ['gate', 'bybit', 'okx']
2025-07-30 22:06:03,046 [INFO]    ✅ initialize_exchanges()成功: 3个交易所
2025-07-30 22:06:03,046 [INFO] 🔍 4. 测试完整的初始化流程...
2025-07-30 22:06:03,047 [INFO]    初始全局交易所状态: None
2025-07-30 22:06:03,047 [INFO] ✅ 交易系统初始化器创建完成
2025-07-30 22:06:03,048 [INFO]    调用initialize_all_systems()...
2025-07-30 22:06:03,049 [INFO] 🚀 初始化所有系统...
2025-07-30 22:06:03,049 [INFO] 🏪 独立初始化交易所...
2025-07-30 22:06:03,050 [INFO] 初始化gate交易所接口，API请求限制: 10/秒
2025-07-30 22:06:03,050 [INFO] 🔧 Gate API限制根源修复为8次/秒，确保30+代币健壮启动
2025-07-30 22:06:03,051 [INFO] 初始化Gate.io交易所接口，API请求限制: 8/秒
2025-07-30 22:06:03,051 [INFO] ✅ Gate.io交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-30 22:06:03,051 [INFO] 🚀 初始化Gate.io交易所（分离账户模式）...
2025-07-30 22:06:03,052 [DEBUG] 复用现有HTTP会话: gate
2025-07-30 22:06:03,414 [INFO] ✅ Gate.io连接成功，服务器时间: ****************
2025-07-30 22:06:03,414 [INFO] ✅ Gate.io使用分离账户模式
2025-07-30 22:06:03,415 [DEBUG] Gate.io请求: GET https://api.gateio.ws/api/v4/spot/accounts, Headers: {'KEY': 'eb321587106cf01dcde93830d7777e2a', 'SIGN': '2e119a5278e0787e108fd93381e62674a17b1336ff03b81a9dd7a32b88a1782f9f196d399825c0e06d8863c68c271c4b5c37499e63f170d2fe6b67891eba5ea7', 'Timestamp': '**********.4153786', 'Content-Type': 'application/json'}, Data: 
2025-07-30 22:06:03,888 [DEBUG] Gate.io API响应成功 (状态码200): [{'currency': 'ICNT', 'available': '0.00695', 'locked': '0', 'update_id': 40}, {'currency': 'HUMA', 'available': '0.00087', 'locked': '0', 'update_id': 14}, {'currency': 'TMAI', 'available': '0.********', 'locked': '0', 'update_id': 53}, {'currency': 'DOGE', 'available': '0.00099', 'locked': '0', 'update_id': 4}, {'currency': 'USDT', 'available': '103.************', 'locked': '0', 'update_id': 1483}, {'currency': 'NEAR', 'available': '0.00799', 'locked': '0', 'update_id': 218}, {'currency': 'GT', 'available': '0.**********', 'locked': '0', 'update_id': 634}, {'currency': 'ALCH', 'available': '0.08', 'locked': '0', 'update_id': 26}, {'currency': 'SPK', 'available': '0.00943', 'locked': '0', 'update_id': 9}, {'currency': 'PEPE', 'available': '0.039', 'locked': '0', 'update_id': 6}, {'currency': '4EVER', 'available': '0.02703235', 'locked': '0', 'update_id': 26}, {'currency': 'GLS', 'available': '0.01086977', 'locked': '0', 'update_id': 219}, {'currency': 'BTC', 'available': '0.00000786', 'locked': '0', 'update_id': 93}, {'currency': 'UNI', 'available': '0.00952', 'locked': '0', 'update_id': 62}, {'currency': 'BNB', 'available': '0.000035', 'locked': '0', 'update_id': 20}, {'currency': 'ETH', 'available': '0.0000901', 'locked': '0', 'update_id': 6}, {'currency': 'RESOLV', 'available': '0.00609', 'locked': '0', 'update_id': 109}, {'currency': 'LINK', 'available': '0.00141', 'locked': '0', 'update_id': 12}, {'currency': 'PI', 'available': '0.00883', 'locked': '0', 'update_id': 4}, {'currency': 'LTC', 'available': '0.0000506', 'locked': '0', 'update_id': 25}, {'currency': 'POINT', 'available': '0.**********', 'locked': '0', 'update_id': 17}, {'currency': 'LAYER', 'available': '0', 'locked': '0', 'update_id': 0}, {'currency': 'ADA', 'available': '0.00216', 'locked': '0', 'update_id': 117}]
2025-07-30 22:06:03,889 [INFO] 🕐 gate 健壮冷却等待: 1.026秒 (要求: 1.500秒)
2025-07-30 22:06:04,922 [DEBUG] Gate.io请求: GET https://api.gateio.ws/api/v4/futures/usdt/accounts, Headers: {'KEY': 'eb321587106cf01dcde93830d7777e2a', 'SIGN': '573c78cbfd6f791ffdc64ea22e2459eab0d4c8fa440afa035fa03ae2160673733dbf38a5ffa936130088442ff1108b515317a9c3b1d0dc1350426f87521b196b', 'Timestamp': '**********.9227123', 'Content-Type': 'application/json'}, Data: 
2025-07-30 22:06:05,282 [DEBUG] Gate.io API响应成功 (状态码200): {'order_margin': '0', 'point': '0', 'bonus': '0', 'history': {'dnw': '-124.********', 'pnl': '262.***********', 'refr': '0', 'point_fee': '0', 'fund': '1.***********', 'bonus_dnw': '0', 'point_refr': '0', 'bonus_offset': '0', 'fee': '-34.********', 'point_dnw': '0', 'cross_settle': '0'}, 'unrealised_pnl': '0', 'total': '104.***********', 'available': '104.***********', 'enable_credit': False, 'in_dual_mode': False, 'currency': 'USDT', 'position_margin': '0', 'user': ********, 'update_time': **********, 'update_id': 1158, 'position_initial_margin': '0', 'maintenance_margin': '0', 'margin_mode': 0, 'enable_evolved_classic': True, 'cross_initial_margin': '0', 'cross_maintenance_margin': '0', 'cross_order_margin': '0', 'cross_unrealised_pnl': '0', 'cross_virtual_unrealised_pnl': '0', 'cross_available': '104.***********', 'isolated_position_margin': '0', 'enable_new_dual_mode': False, 'margin_mode_name': 'classic', 'cross_margin_balance': '104.***********', 'cross_mmr': '0', 'cross_imr': '0', 'enable_tiered_mm': False, 'position_voucher_total': '0'}
2025-07-30 22:06:05,283 [INFO] ✅ Gate.io初始余额: 现货103.44 + 期货104.64 = 总计208.08 USDT
2025-07-30 22:06:05,284 [INFO] ✅ Gate.io分离账户模式已确认
2025-07-30 22:06:05,284 [INFO] ✅ Gate.io交易所初始化完成
2025-07-30 22:06:05,285 [INFO] ✅ Gate.io初始化成功
2025-07-30 22:06:05,286 [INFO] 初始化bybit交易所接口，API请求限制: 6/秒
2025-07-30 22:06:05,286 [INFO] 🔧 Bybit API限制根源修复为4次/秒，确保30+代币健壮启动
2025-07-30 22:06:05,286 [INFO] 初始化Bybit交易所接口，API请求限制: 4/秒
2025-07-30 22:06:05,287 [INFO] ✅ Bybit交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-30 22:06:05,287 [DEBUG] Bybit交易所初始化完成: 杠杆=3x, 最小价值=35.0USDT
2025-07-30 22:06:05,288 [INFO] 🚀 初始化Bybit交易所（统一账户模式）...
2025-07-30 22:06:05,289 [DEBUG] 复用现有HTTP会话: bybit
2025-07-30 22:06:05,289 [DEBUG] Bybit请求: GET https://api.bybit.com/v5/market/time
2025-07-30 22:06:05,290 [DEBUG] Bybit请求头: {'Content-Type': 'application/json'}
2025-07-30 22:06:05,290 [DEBUG] Bybit请求参数: {}
2025-07-30 22:06:05,531 [ERROR] Bybit请求失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/time'
2025-07-30 22:06:05,532 [WARNING] 获取Bybit服务器时间失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/time'，使用本地时间
2025-07-30 22:06:05,532 [INFO] ✅ Bybit连接成功，服务器时间: *************
2025-07-30 22:06:05,533 [INFO] ✅ Bybit使用统一账户模式
2025-07-30 22:06:05,533 [INFO] Bybit查询余额: 原始account_type=unified, 强制使用category=UNIFIED
2025-07-30 22:06:05,534 [INFO] 🕐 bybit 健壮冷却等待: 1.255秒 (要求: 1.500秒)
2025-07-30 22:06:07,036 [ERROR] Bybit同步时间异常: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/market/time'
2025-07-30 22:06:07,036 [WARNING] Bybit时间同步失败，使用本地时间
2025-07-30 22:06:07,037 [DEBUG] Bybit固定时间戳: *************
2025-07-30 22:06:07,037 [DEBUG] Bybit签名构建:
2025-07-30 22:06:07,038 [DEBUG]   timestamp: *************
2025-07-30 22:06:07,039 [DEBUG]   api_key: lYC8LeR8***
2025-07-30 22:06:07,039 [DEBUG]   recv_window: 5000
2025-07-30 22:06:07,040 [DEBUG]   param_str: accountType=UNIFIED
2025-07-30 22:06:07,040 [DEBUG]   完整签名字符串长度: 54
2025-07-30 22:06:07,041 [DEBUG]   生成的签名: 423eca8359f6cb71...
2025-07-30 22:06:07,041 [DEBUG] Bybit请求: GET https://api.bybit.com/v5/account/wallet-balance?accountType=UNIFIED
2025-07-30 22:06:07,042 [DEBUG] Bybit请求头: {'Content-Type': 'application/json', 'X-BAPI-API-KEY': 'lYC8LeR8sWnAiDZFrZ', 'X-BAPI-TIMESTAMP': '*************', 'X-BAPI-RECV-WINDOW': '5000', 'X-BAPI-SIGN-TYPE': '2', 'X-BAPI-SIGN': '423eca8359f6cb712411c858f16bb79a2a10577e57b3cbfaffcafa843c8997e1'}
2025-07-30 22:06:07,043 [DEBUG] Bybit请求参数: {'accountType': 'UNIFIED'}
2025-07-30 22:06:07,278 [ERROR] Bybit请求失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/account/wallet-balance?accountType=UNIFIED'
2025-07-30 22:06:07,279 [ERROR] 获取Bybit余额失败: 403, message='Attempt to decode JSON with unexpected mimetype: text/html', url='https://api.bybit.com/v5/account/wallet-balance?accountType=UNIFIED'
2025-07-30 22:06:07,280 [INFO] ✅ Bybit初始余额: 0.00 USDT
2025-07-30 22:06:07,280 [INFO] ✅ Bybit统一账户模式已激活
2025-07-30 22:06:07,280 [INFO] ✅ Bybit交易所初始化完成
2025-07-30 22:06:07,281 [INFO] ✅ Bybit初始化成功
2025-07-30 22:06:07,281 [INFO] 初始化OKX交易所接口，API请求限制: 5/秒
2025-07-30 22:06:07,283 [DEBUG] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 22:06:07,284 [INFO] 🔧 OKX API限制根源修复为2次/秒，确保30+代币健壮启动
2025-07-30 22:06:07,284 [INFO] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=2次/秒
2025-07-30 22:06:07,285 [INFO] 🔥 开始初始化OKX账户配置...
2025-07-30 22:06:07,285 [DEBUG] 复用现有HTTP会话: okx
2025-07-30 22:06:07,286 [INFO] 🕐 okx 健壮冷却等待: 0.005秒 (要求: 5.000秒)
2025-07-30 22:06:08,111 [INFO] OKX当前账户模式: 2
2025-07-30 22:06:08,111 [WARNING] OKX账户不是跨币种保证金模式，当前模式: 2
2025-07-30 22:06:08,112 [INFO] 🕐 okx 健壮冷却等待: 4.184秒 (要求: 5.000秒)
2025-07-30 22:06:12,735 [INFO] OKX设置为单向持仓模式
2025-07-30 22:06:12,736 [INFO] 📋 使用缓存的交易对: 10个
2025-07-30 22:06:12,736 [INFO] OKX设置杠杆: SPK-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 22:06:12,737 [INFO] 🕐 okx 健壮冷却等待: 4.574秒 (要求: 5.000秒)
2025-07-30 22:06:17,726 [INFO] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 22:06:17,727 [INFO] OKX持仓模式: net_mode
2025-07-30 22:06:17,727 [INFO] 🕐 okx 健壮冷却等待: 4.597秒 (要求: 5.000秒)
2025-07-30 22:06:22,735 [INFO] OKX设置net杠杆成功: 3倍
2025-07-30 22:06:22,736 [INFO] OKX设置杠杆成功: 3倍
2025-07-30 22:06:22,736 [DEBUG] OKX预设置杠杆成功: SPK-USDT
2025-07-30 22:06:22,737 [INFO] OKX设置杠杆: RESOLV-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 22:06:22,737 [INFO] 🕐 okx 健壮冷却等待: 4.592秒 (要求: 5.000秒)
2025-07-30 22:06:27,740 [INFO] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 22:06:27,741 [INFO] OKX持仓模式: net_mode
2025-07-30 22:06:27,741 [INFO] 🕐 okx 健壮冷却等待: 4.596秒 (要求: 5.000秒)
2025-07-30 22:06:32,757 [INFO] OKX设置net杠杆成功: 3倍
2025-07-30 22:06:32,757 [INFO] OKX设置杠杆成功: 3倍
2025-07-30 22:06:32,758 [DEBUG] OKX预设置杠杆成功: RESOLV-USDT
2025-07-30 22:06:32,758 [INFO] OKX设置杠杆: ICNT-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 22:06:32,759 [INFO] 🕐 okx 健壮冷却等待: 4.589秒 (要求: 5.000秒)
2025-07-30 22:06:37,744 [INFO] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 22:06:37,745 [INFO] OKX持仓模式: net_mode
2025-07-30 22:06:37,746 [INFO] 🕐 okx 健壮冷却等待: 4.599秒 (要求: 5.000秒)
2025-07-30 22:06:42,748 [INFO] OKX设置net杠杆成功: 3倍
2025-07-30 22:06:42,748 [INFO] OKX设置杠杆成功: 3倍
2025-07-30 22:06:42,749 [DEBUG] OKX预设置杠杆成功: ICNT-USDT
2025-07-30 22:06:42,749 [INFO] OKX设置杠杆: CAKE-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 22:06:42,749 [INFO] 🕐 okx 健壮冷却等待: 4.586秒 (要求: 5.000秒)
2025-07-30 22:06:47,759 [INFO] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 22:06:47,760 [INFO] OKX持仓模式: net_mode
2025-07-30 22:06:47,760 [INFO] 🕐 okx 健壮冷却等待: 4.593秒 (要求: 5.000秒)
