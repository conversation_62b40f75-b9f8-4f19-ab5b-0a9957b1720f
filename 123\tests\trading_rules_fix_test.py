#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交易规则修复测试脚本
测试修复后的交易规则预加载器是否能正确使用全局交易所实例
"""

import sys
import os
import asyncio
import logging
from dotenv import load_dotenv

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 加载环境变量
load_dotenv()

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s'
)

async def test_trading_rules_fix():
    """测试交易规则修复"""
    logger = logging.getLogger(__name__)
    logger.info("🔍 开始测试交易规则修复...")
    
    try:
        # 1. 初始化交易系统
        logger.info("📋 步骤1: 初始化交易系统...")
        from core.trading_system_initializer import TradingSystemInitializer, get_global_exchanges
        
        initializer = TradingSystemInitializer()
        success = await initializer.initialize_all_systems()
        
        if not success:
            logger.error("❌ 交易系统初始化失败")
            return False
            
        # 2. 检查全局交易所实例
        logger.info("📋 步骤2: 检查全局交易所实例...")
        global_exchanges = get_global_exchanges()
        
        if not global_exchanges:
            logger.error("❌ 全局交易所实例为空")
            return False
            
        logger.info(f"✅ 全局交易所实例: {list(global_exchanges.keys())}")
        
        # 3. 测试交易规则获取
        logger.info("📋 步骤3: 测试交易规则获取...")
        from core.trading_rules_preloader import get_trading_rules_preloader
        
        preloader = get_trading_rules_preloader()
        
        # 测试SPK-USDT_gate_spot
        test_cases = [
            ("gate", "SPK-USDT", "spot"),
            ("bybit", "SPK-USDT", "spot"),
            ("okx", "SPK-USDT", "spot")
        ]
        
        success_count = 0
        for exchange, symbol, market_type in test_cases:
            logger.info(f"   测试: {exchange}_{symbol}_{market_type}")
            
            try:
                rule = preloader.get_trading_rule(exchange, symbol, market_type)
                if rule:
                    logger.info(f"   ✅ 成功获取交易规则: {rule.qty_step}")
                    success_count += 1
                else:
                    logger.warning(f"   ❌ 无法获取交易规则")
            except Exception as e:
                logger.error(f"   ❌ 获取交易规则异常: {e}")
                
        logger.info(f"📋 测试结果: {success_count}/{len(test_cases)} 成功")
        
        if success_count > 0:
            logger.info("🎉 修复成功！交易规则可以正常获取")
            return True
        else:
            logger.error("❌ 修复失败，所有交易规则获取都失败")
            return False
            
    except Exception as e:
        logger.error(f"❌ 测试异常: {e}")
        return False

async def main():
    """主函数"""
    success = await test_trading_rules_fix()
    if success:
        print("\n🎉 交易规则修复测试通过！")
    else:
        print("\n❌ 交易规则修复测试失败！")

if __name__ == "__main__":
    asyncio.run(main())
