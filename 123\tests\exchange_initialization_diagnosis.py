#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交易所初始化诊断脚本
专门诊断为什么全局交易所实例为空的问题
"""

import sys
import os
import asyncio
import logging
import time

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('exchange_initialization_diagnosis.log', encoding='utf-8')
    ]
)

class ExchangeInitializationDiagnosis:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.diagnosis_results = {}
        
    async def run_diagnosis(self):
        """运行完整诊断"""
        self.logger.info("🔍 开始交易所初始化诊断...")
        
        # 1. 检查环境变量
        await self._check_environment_variables()
        
        # 2. 测试单独的交易所初始化
        await self._test_individual_exchange_initialization()
        
        # 3. 测试initialize_exchanges方法
        await self._test_initialize_exchanges_method()
        
        # 4. 测试完整的initialize_all_systems流程
        await self._test_full_initialization_flow()
        
        # 5. 生成诊断报告
        self._generate_diagnosis_report()
        
    async def _check_environment_variables(self):
        """检查环境变量配置"""
        self.logger.info("🔍 1. 检查环境变量配置...")
        
        required_vars = [
            "GATE_API_KEY", "GATE_API_SECRET",
            "BYBIT_API_KEY", "BYBIT_API_SECRET", 
            "OKX_API_KEY", "OKX_API_SECRET", "OKX_API_PASSPHRASE"
        ]
        
        env_status = {}
        for var in required_vars:
            value = os.getenv(var)
            env_status[var] = bool(value and len(value.strip()) > 0)
            
        configured_count = sum(env_status.values())
        self.diagnosis_results["environment"] = {
            "configured_count": configured_count,
            "total_required": len(required_vars),
            "details": env_status
        }
        
        self.logger.info(f"   环境变量配置: {configured_count}/{len(required_vars)}")
        
        # 检查每个交易所的配置完整性
        gate_complete = env_status["GATE_API_KEY"] and env_status["GATE_API_SECRET"]
        bybit_complete = env_status["BYBIT_API_KEY"] and env_status["BYBIT_API_SECRET"]
        okx_complete = env_status["OKX_API_KEY"] and env_status["OKX_API_SECRET"] and env_status["OKX_API_PASSPHRASE"]
        
        self.logger.info(f"   Gate.io配置: {'✅' if gate_complete else '❌'}")
        self.logger.info(f"   Bybit配置: {'✅' if bybit_complete else '❌'}")
        self.logger.info(f"   OKX配置: {'✅' if okx_complete else '❌'}")
        
    async def _test_individual_exchange_initialization(self):
        """测试单独的交易所初始化"""
        self.logger.info("🔍 2. 测试单独的交易所初始化...")
        
        exchange_results = {}
        
        # 测试Gate.io
        try:
            gate_api_key = os.getenv("GATE_API_KEY")
            gate_api_secret = os.getenv("GATE_API_SECRET")
            
            if gate_api_key and gate_api_secret:
                self.logger.info("   测试Gate.io初始化...")
                from exchanges.gate_exchange import GateExchange
                gate_exchange = GateExchange(gate_api_key, gate_api_secret)
                await gate_exchange.initialize()
                exchange_results["gate"] = "✅ 成功"
                self.logger.info("   ✅ Gate.io初始化成功")
            else:
                exchange_results["gate"] = "❌ API密钥缺失"
                self.logger.warning("   ❌ Gate.io API密钥缺失")
                
        except Exception as e:
            exchange_results["gate"] = f"❌ 初始化失败: {str(e)}"
            self.logger.error(f"   ❌ Gate.io初始化失败: {e}")
            
        # 测试Bybit
        try:
            bybit_api_key = os.getenv("BYBIT_API_KEY")
            bybit_api_secret = os.getenv("BYBIT_API_SECRET")
            
            if bybit_api_key and bybit_api_secret:
                self.logger.info("   测试Bybit初始化...")
                from exchanges.bybit_exchange import BybitExchange
                bybit_exchange = BybitExchange(bybit_api_key, bybit_api_secret)
                await bybit_exchange.initialize()
                exchange_results["bybit"] = "✅ 成功"
                self.logger.info("   ✅ Bybit初始化成功")
            else:
                exchange_results["bybit"] = "❌ API密钥缺失"
                self.logger.warning("   ❌ Bybit API密钥缺失")
                
        except Exception as e:
            exchange_results["bybit"] = f"❌ 初始化失败: {str(e)}"
            self.logger.error(f"   ❌ Bybit初始化失败: {e}")
            
        # 测试OKX
        try:
            okx_api_key = os.getenv("OKX_API_KEY")
            okx_api_secret = os.getenv("OKX_API_SECRET")
            okx_api_passphrase = os.getenv("OKX_API_PASSPHRASE")
            
            if okx_api_key and okx_api_secret and okx_api_passphrase:
                self.logger.info("   测试OKX初始化...")
                from exchanges.okx_exchange import OKXExchange
                okx_exchange = OKXExchange(okx_api_key, okx_api_secret, okx_api_passphrase)
                await okx_exchange.initialize()
                exchange_results["okx"] = "✅ 成功"
                self.logger.info("   ✅ OKX初始化成功")
            else:
                exchange_results["okx"] = "❌ API密钥缺失"
                self.logger.warning("   ❌ OKX API密钥缺失")
                
        except Exception as e:
            exchange_results["okx"] = f"❌ 初始化失败: {str(e)}"
            self.logger.error(f"   ❌ OKX初始化失败: {e}")
            
        self.diagnosis_results["individual_exchanges"] = exchange_results
        
    async def _test_initialize_exchanges_method(self):
        """测试initialize_exchanges方法"""
        self.logger.info("🔍 3. 测试initialize_exchanges方法...")
        
        try:
            from core.trading_system_initializer import TradingSystemInitializer
            initializer = TradingSystemInitializer()
            
            self.logger.info("   调用initialize_exchanges()...")
            exchanges = await initializer.initialize_exchanges()
            
            if exchanges:
                self.diagnosis_results["initialize_exchanges"] = {
                    "status": "✅ 成功",
                    "count": len(exchanges),
                    "exchanges": list(exchanges.keys())
                }
                self.logger.info(f"   ✅ initialize_exchanges()成功: {len(exchanges)}个交易所")
            else:
                self.diagnosis_results["initialize_exchanges"] = {
                    "status": "❌ 失败",
                    "result": "返回空字典或None"
                }
                self.logger.error("   ❌ initialize_exchanges()返回空结果")
                
        except Exception as e:
            self.diagnosis_results["initialize_exchanges"] = {
                "status": "❌ 异常",
                "error": str(e)
            }
            self.logger.error(f"   ❌ initialize_exchanges()异常: {e}")
            
    async def _test_full_initialization_flow(self):
        """测试完整的初始化流程"""
        self.logger.info("🔍 4. 测试完整的初始化流程...")
        
        try:
            from core.trading_system_initializer import TradingSystemInitializer, get_global_exchanges
            
            # 检查初始状态
            initial_global = get_global_exchanges()
            self.logger.info(f"   初始全局交易所状态: {initial_global}")
            
            # 创建初始化器并运行完整流程
            initializer = TradingSystemInitializer()
            self.logger.info("   调用initialize_all_systems()...")
            
            success = await initializer.initialize_all_systems()
            
            # 检查结果状态
            final_global = get_global_exchanges()
            
            self.diagnosis_results["full_initialization"] = {
                "success": success,
                "initial_global": str(initial_global),
                "final_global": str(final_global),
                "final_global_type": type(final_global).__name__,
                "final_global_length": len(final_global) if final_global else 0
            }
            
            self.logger.info(f"   initialize_all_systems()结果: {success}")
            self.logger.info(f"   最终全局交易所状态: {final_global}")
            
        except Exception as e:
            self.diagnosis_results["full_initialization"] = {
                "status": "❌ 异常",
                "error": str(e)
            }
            self.logger.error(f"   ❌ 完整初始化流程异常: {e}")
            
    def _generate_diagnosis_report(self):
        """生成诊断报告"""
        self.logger.info("📋 生成诊断报告...")
        
        print("\n" + "="*80)
        print("🔍 交易所初始化诊断报告")
        print("="*80)
        
        # 环境变量状态
        env_result = self.diagnosis_results.get("environment", {})
        print(f"\n📋 环境变量配置: {env_result.get('configured_count', 0)}/{env_result.get('total_required', 7)}")
        
        # 单独交易所初始化状态
        individual_result = self.diagnosis_results.get("individual_exchanges", {})
        print(f"\n📋 单独交易所初始化:")
        for exchange, status in individual_result.items():
            print(f"   {exchange}: {status}")
            
        # initialize_exchanges方法状态
        init_exchanges_result = self.diagnosis_results.get("initialize_exchanges", {})
        print(f"\n📋 initialize_exchanges方法: {init_exchanges_result.get('status', '未测试')}")
        
        # 完整初始化流程状态
        full_init_result = self.diagnosis_results.get("full_initialization", {})
        print(f"\n📋 完整初始化流程:")
        print(f"   成功: {full_init_result.get('success', False)}")
        print(f"   初始全局状态: {full_init_result.get('initial_global', 'N/A')}")
        print(f"   最终全局状态: {full_init_result.get('final_global', 'N/A')}")
        
        # 根本原因分析
        print(f"\n🎯 根本原因分析:")
        if full_init_result.get("final_global") == "{}":
            print("   ❌ 核心问题: initialize_exchanges()返回空字典")
            print("   📍 影响: 导致set_global_exchanges({})设置空的全局实例")
            print("   🔧 解决方案: 检查API密钥配置或交易所初始化逻辑")
        elif full_init_result.get("final_global") == "None":
            print("   ❌ 核心问题: 全局交易所实例未被设置")
            print("   📍 影响: initialize_all_systems()可能未正确执行")
            print("   🔧 解决方案: 检查系统初始化流程")
        else:
            print("   ✅ 全局交易所实例设置正常")
            
        print("\n✅ 诊断完成，详细日志已保存到 exchange_initialization_diagnosis.log")
        print("="*80)

async def main():
    """主函数"""
    diagnosis = ExchangeInitializationDiagnosis()
    await diagnosis.run_diagnosis()

if __name__ == "__main__":
    asyncio.run(main())
