#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔥 综合问题诊断脚本 - SPK-USDT_gate_spot交易规则获取失败
按照用户要求进行精准定位问题
"""

import sys
import os
import asyncio
import logging
import time
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('comprehensive_diagnosis.log')
    ]
)

logger = logging.getLogger(__name__)

class ComprehensiveDiagnosis:
    """综合问题诊断器"""
    
    def __init__(self):
        self.diagnosis_results = {}
        self.logger = logger
        
    async def run_diagnosis(self):
        """运行完整诊断"""
        self.logger.info("🔍 开始综合问题诊断...")
        
        # 1. 检查全局交易所实例状态
        await self._check_global_exchanges_status()
        
        # 2. 检查交易规则预加载器状态
        await self._check_trading_rules_preloader_status()
        
        # 3. 检查环境变量配置
        await self._check_environment_variables()
        
        # 4. 检查系统初始化顺序
        await self._check_initialization_order()
        
        # 5. 模拟SPK-USDT_gate_spot规则获取
        await self._simulate_spk_usdt_rule_fetch()
        
        # 6. 生成诊断报告
        self._generate_diagnosis_report()
        
    async def _check_global_exchanges_status(self):
        """检查全局交易所实例状态"""
        self.logger.info("🔍 1. 检查全局交易所实例状态...")
        
        try:
            from core.trading_system_initializer import get_global_exchanges
            global_exchanges = get_global_exchanges()
            
            if global_exchanges is None:
                self.diagnosis_results["global_exchanges"] = {
                    "status": "❌ 问题发现",
                    "issue": "get_global_exchanges()返回None",
                    "impact": "交易规则预加载器无法获取交易所实例",
                    "solution": "需要在系统初始化时调用set_global_exchanges()"
                }
                self.logger.error("   ❌ get_global_exchanges()返回None")
            elif isinstance(global_exchanges, dict) and len(global_exchanges) == 0:
                self.diagnosis_results["global_exchanges"] = {
                    "status": "❌ 问题发现",
                    "issue": "get_global_exchanges()返回空字典{}",
                    "impact": "交易所初始化失败，导致全局实例为空",
                    "solution": "检查API密钥配置和交易所初始化过程"
                }
                self.logger.error("   ❌ get_global_exchanges()返回空字典{}")
            else:
                self.diagnosis_results["global_exchanges"] = {
                    "status": "✅ 正常",
                    "count": len(global_exchanges),
                    "exchanges": list(global_exchanges.keys())
                }
                self.logger.info(f"   ✅ get_global_exchanges()返回{len(global_exchanges)}个交易所")
                
        except Exception as e:
            self.diagnosis_results["global_exchanges"] = {
                "status": "❌ 异常",
                "error": str(e)
            }
            self.logger.error(f"   ❌ 检查全局交易所实例异常: {e}")
    
    async def _check_trading_rules_preloader_status(self):
        """检查交易规则预加载器状态"""
        self.logger.info("🔍 2. 检查交易规则预加载器状态...")
        
        try:
            from core.trading_rules_preloader import get_trading_rules_preloader
            preloader = get_trading_rules_preloader()
            
            # 检查预加载器是否存在
            if preloader is None:
                self.diagnosis_results["preloader"] = {
                    "status": "❌ 问题发现",
                    "issue": "交易规则预加载器为None"
                }
                self.logger.error("   ❌ 交易规则预加载器为None")
                return
            
            # 检查预加载器方法
            has_get_method = hasattr(preloader, 'get_trading_rule')
            has_preload_method = hasattr(preloader, 'preload_all_trading_rules')
            
            self.diagnosis_results["preloader"] = {
                "status": "✅ 正常" if has_get_method and has_preload_method else "⚠️ 部分问题",
                "has_get_method": has_get_method,
                "has_preload_method": has_preload_method,
                "cache_size": len(preloader.trading_rules) if hasattr(preloader, 'trading_rules') else 0
            }
            
            self.logger.info(f"   ✅ 预加载器存在，缓存大小: {len(preloader.trading_rules) if hasattr(preloader, 'trading_rules') else 0}")
            
        except Exception as e:
            self.diagnosis_results["preloader"] = {
                "status": "❌ 异常",
                "error": str(e)
            }
            self.logger.error(f"   ❌ 检查交易规则预加载器异常: {e}")
    
    async def _check_environment_variables(self):
        """检查环境变量配置"""
        self.logger.info("🔍 3. 检查环境变量配置...")
        
        required_vars = [
            "GATE_API_KEY", "GATE_API_SECRET",
            "BYBIT_API_KEY", "BYBIT_API_SECRET", 
            "OKX_API_KEY", "OKX_API_SECRET", "OKX_API_PASSPHRASE"
        ]
        
        env_status = {}
        configured_count = 0
        
        for var in required_vars:
            value = os.getenv(var)
            is_configured = bool(value and value.strip())
            env_status[var] = is_configured
            if is_configured:
                configured_count += 1
        
        self.diagnosis_results["environment"] = {
            "status": "✅ 完整配置" if configured_count == len(required_vars) else f"⚠️ 部分配置 ({configured_count}/{len(required_vars)})",
            "configured_count": configured_count,
            "total_required": len(required_vars),
            "details": env_status
        }
        
        self.logger.info(f"   环境变量配置: {configured_count}/{len(required_vars)}")
        
    async def _check_initialization_order(self):
        """检查系统初始化顺序"""
        self.logger.info("🔍 4. 检查系统初始化顺序...")
        
        try:
            from core.trading_system_initializer import TradingSystemInitializer
            import inspect
            
            # 获取initialize_trading_system方法源码
            initializer = TradingSystemInitializer()
            source = inspect.getsource(initializer.initialize_trading_system)
            
            # 检查set_global_exchanges调用是否在预加载之前
            lines = source.split('\n')
            set_line = -1
            preload_line = -1
            
            for i, line in enumerate(lines):
                if "set_global_exchanges" in line:
                    set_line = i
                if "preload_all_trading_rules" in line:
                    preload_line = i
            
            order_correct = set_line != -1 and preload_line != -1 and set_line < preload_line
            
            self.diagnosis_results["initialization_order"] = {
                "status": "✅ 正确" if order_correct else "❌ 错误",
                "set_global_line": set_line,
                "preload_line": preload_line,
                "order_correct": order_correct
            }
            
            if order_correct:
                self.logger.info(f"   ✅ 初始化顺序正确: set_global_exchanges在第{set_line}行，预加载在第{preload_line}行")
            else:
                self.logger.error(f"   ❌ 初始化顺序错误: set_global_exchanges在第{set_line}行，预加载在第{preload_line}行")
                
        except Exception as e:
            self.diagnosis_results["initialization_order"] = {
                "status": "❌ 异常",
                "error": str(e)
            }
            self.logger.error(f"   ❌ 检查初始化顺序异常: {e}")
    
    async def _simulate_spk_usdt_rule_fetch(self):
        """模拟SPK-USDT_gate_spot规则获取"""
        self.logger.info("🔍 5. 模拟SPK-USDT_gate_spot规则获取...")
        
        try:
            from core.trading_rules_preloader import get_trading_rules_preloader
            preloader = get_trading_rules_preloader()
            
            if preloader is None:
                self.diagnosis_results["spk_simulation"] = {
                    "status": "❌ 失败",
                    "reason": "预加载器为None"
                }
                return
            
            # 尝试获取SPK-USDT_gate_spot规则
            start_time = time.time()
            rule = preloader.get_trading_rule("gate", "SPK-USDT", "spot")
            duration = (time.time() - start_time) * 1000
            
            if rule is not None:
                self.diagnosis_results["spk_simulation"] = {
                    "status": "✅ 成功",
                    "rule_found": True,
                    "duration_ms": duration,
                    "rule_details": {
                        "qty_step": str(rule.qty_step),
                        "exchange": rule.exchange,
                        "market_type": rule.market_type,
                        "source": rule.source
                    }
                }
                self.logger.info(f"   ✅ SPK-USDT_gate_spot规则获取成功，耗时{duration:.1f}ms")
            else:
                self.diagnosis_results["spk_simulation"] = {
                    "status": "❌ 失败",
                    "rule_found": False,
                    "duration_ms": duration,
                    "reason": "规则为None"
                }
                self.logger.error(f"   ❌ SPK-USDT_gate_spot规则获取失败，耗时{duration:.1f}ms")
                
        except Exception as e:
            self.diagnosis_results["spk_simulation"] = {
                "status": "❌ 异常",
                "error": str(e)
            }
            self.logger.error(f"   ❌ 模拟规则获取异常: {e}")
    
    def _generate_diagnosis_report(self):
        """生成诊断报告"""
        self.logger.info("📋 生成诊断报告...")
        
        print("\n" + "="*80)
        print("🔍 SPK-USDT_gate_spot交易规则获取失败 - 综合诊断报告")
        print("="*80)
        
        # 统计问题数量
        total_checks = len(self.diagnosis_results)
        problem_count = 0
        
        for check_name, result in self.diagnosis_results.items():
            status = result.get("status", "未知")
            if "❌" in status:
                problem_count += 1
        
        print(f"\n📊 诊断概览:")
        print(f"   总检查项: {total_checks}")
        print(f"   发现问题: {problem_count}")
        print(f"   问题率: {problem_count/total_checks*100:.1f}%")
        
        # 详细结果
        print(f"\n📋 详细诊断结果:")
        for i, (check_name, result) in enumerate(self.diagnosis_results.items(), 1):
            status = result.get("status", "未知")
            print(f"\n{i}. {check_name.replace('_', ' ').title()}: {status}")
            
            # 显示详细信息
            for key, value in result.items():
                if key != "status":
                    print(f"   {key}: {value}")
        
        # 根本原因分析
        print(f"\n🎯 根本原因分析:")
        if self.diagnosis_results.get("global_exchanges", {}).get("status") == "❌ 问题发现":
            print("   ❌ 核心问题: get_global_exchanges()返回None")
            print("   📍 影响: 交易规则预加载器无法获取交易所实例进行API调用")
            print("   🔧 解决方案: 在系统初始化时调用set_global_exchanges()设置全局交易所实例")
        
        if self.diagnosis_results.get("environment", {}).get("configured_count", 0) == 0:
            print("   ⚠️ 环境问题: API密钥配置不完整")
            print("   📍 影响: 无法创建临时交易所实例进行API调用")
            print("   🔧 解决方案: 配置完整的API密钥信息")
        
        if self.diagnosis_results.get("initialization_order", {}).get("order_correct") == False:
            print("   ❌ 顺序问题: 系统初始化顺序错误")
            print("   📍 影响: 预加载时全局交易所实例尚未设置")
            print("   🔧 解决方案: 确保set_global_exchanges在preload_all_trading_rules之前调用")
        
        print(f"\n✅ 诊断完成，详细日志已保存到 comprehensive_diagnosis.log")
        print("="*80)

async def main():
    """主函数"""
    diagnosis = ComprehensiveDiagnosis()
    await diagnosis.run_diagnosis()

if __name__ == "__main__":
    asyncio.run(main())
